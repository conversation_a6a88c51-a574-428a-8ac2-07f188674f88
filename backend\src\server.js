const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
require("dotenv").config();

const app = express();

// Security middleware
app.use(helmet());

// CORS - Cho phép frontend gọi API
app.use(cors());

//Logging middleware (chỉ trong development)
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Basic middleware
app.use(express.json());

// Test route
app.get("/", (req, res) => {
  res.json({
    message: "E-commerce API is working!",
    timestamp: new Date().toISOString(),
  });
});

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    message: "Server is running!",
    timestamp: new Date().toISOString(),
  });
});

// 404 handler - Bắt lỗi route không tồn tại
// app.use("*", (req, res) => {
//   res.status(404).json({
//     status: "error",
//     message: `Route ${req.originalUrl} not found`,
//     timestamp: new Date().toISOString(),
//   });
// });

// // Global error handler - Bắt các lỗi khác
// app.use((err, req, res, next) => {
//   console.error("Error:", err);

//   res.status(err.status || 500).json({
//     status: "error",
//     message: err.message || "Internal Server Error",
//     timestamp: new Date().toISOString(),
//     ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
//   });
// });

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log("=".repeat(50));
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📍 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`🔗 Test: http://localhost:${PORT}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
  console.log("=".repeat(50));
});
